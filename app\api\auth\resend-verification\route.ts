import { NextRequest, NextResponse } from 'next/server'
import { log } from '@/app/lib/logger';
import { prisma } from '@/app/lib/db'
import { sendVerificationEmail, generateVerificationToken, generateTokenExpiry } from '@/app/lib/email'

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: '邮箱地址是必填的' },
        { status: 400 }
      )
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 检查邮箱是否已经验证
    if (user.emailVerified) {
      return NextResponse.json(
        { error: '邮箱已经验证过了' },
        { status: 400 }
      )
    }

    // 生成新的验证令牌
    const verificationToken = generateVerificationToken()
    const verificationExpires = generateTokenExpiry()

    // 更新用户的验证令牌
    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerificationToken: verificationToken,
        emailVerificationExpires: verificationExpires
      }
    })

    // 发送验证邮件
    try {
      await sendVerificationEmail(email, verificationToken)
    } catch (error) {
      log.error('邮件发送失败:', error)
      return NextResponse.json(
        { error: '邮件发送失败，请稍后重试' },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { message: '验证邮件已重新发送，请检查您的邮箱' },
      { status: 200 }
    )
  } catch (error) {
    log.error('重新发送验证邮件错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
