/**
 * 验证服务测试文件
 * 用于测试各种验证功能的正确性
 */

import { ExcelValidationService } from './validation'

import { log } from '@/app/lib/logger';
// 模拟Univer API
class MockUniverAPI {
  private mockData: Record<string, unknown> = {}

  constructor(mockData: Record<string, unknown> = {}) {
    this.mockData = mockData
  }

  getActiveWorkbook() {
    return {
      getActiveSheet: () => ({
        getSheetId: () => 'mock-sheet-id',
        getName: () => 'Mock Sheet',
        getCellData: () => ({}),
        getCell: () => ({}),
        getFilter: () => null,
        getRange: (cell: string) => ({
          getValue: () => (this.mockData[cell] as { value?: { v: unknown } })?.value || null,
          getValues: () => [[]],
          getFormula: () => '',
          getFormulas: () => [[]],
          getBackgroundColor: () => '',
          getBackgroundColors: () => [[]],
          getCellData: () => this.mockData[cell] || {},
          getNumberFormat: () => '',
          getDisplayValue: () => '',
          getConditionalFormattingRules: () => [],
          isMerged: () => false,
          getWrap: () => false,
        })
      })
    }
  }
}

// 测试用例
export async function runValidationTests() {
  log.validation('开始验证服务测试...')
  
  // 测试1: 单元格值验证
  log.debug('\n=== 测试1: 单元格值验证 ===')
  const mockAPI1 = new MockUniverAPI({
    'A1': { value: { v: 'Hello Excel' } }
  })
  
  const service1 = new ExcelValidationService(mockAPI1)
  const result1 = await service1.validateTask({
    type: 'cellValue',
    cell: 'A1',
    expectedValue: 'Hello Excel'
  })
  
  log.debug('结果1:', result1)
  log.debug('期望: success = true')
  
  // 测试2: 公式验证
  log.debug('\n=== 测试2: 公式验证 ===')
  const mockAPI2 = new MockUniverAPI({
    'B3': { 
      f: '=B1+B2',
      value: { v: 30 }
    }
  })
  
  const service2 = new ExcelValidationService(mockAPI2)
  const result2 = await service2.validateTask({
    type: 'cellFormula',
    cell: 'B3',
    expectedFormula: '=B1+B2',
    expectedValue: 30
  })
  
  log.debug('结果2:', result2)
  log.debug('期望: success = true')
  
  // 测试3: 样式验证
  log.debug('\n=== 测试3: 样式验证 ===')
  const mockAPI3 = new MockUniverAPI({
    'B1': {
      s: {
        bl: 1, // 粗体
        it: 0, // 非斜体
        cl: { rgb: '#FF0000' }, // 红色字体
      }
    }
  })
  
  const service3 = new ExcelValidationService(mockAPI3)
  const result3 = await service3.validateTask({
    type: 'cellStyle',
    cell: 'B1',
    expectedStyle: {
      bold: true,
      color: '#FF0000'
    }
  })
  
  log.debug('结果3:', result3)
  log.debug('期望: success = true')
  
  // 测试4: 错误情况 - 值不匹配
  log.debug('\n=== 测试4: 错误情况 - 值不匹配 ===')
  const mockAPI4 = new MockUniverAPI({
    'A1': { value: { v: 'Wrong Value' } }
  })
  
  const service4 = new ExcelValidationService(mockAPI4)
  const result4 = await service4.validateTask({
    type: 'cellValue',
    cell: 'A1',
    expectedValue: 'Hello Excel'
  })
  
  log.debug('结果4:', result4)
  log.debug('期望: success = false')
  
  log.debug('\n验证服务测试完成！')
}

// 如果直接运行此文件，执行测试
// 只有在Node.js环境中直接运行此文件时才执行测试
if (typeof window === 'undefined' &&
    typeof process !== 'undefined' &&
    process.argv &&
    process.argv[1] &&
    process.argv[1].endsWith('validation.test.ts')) {
  runValidationTests().catch(console.error)
}