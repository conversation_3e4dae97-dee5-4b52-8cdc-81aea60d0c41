import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: false, // 禁用严格模式以避免Univer组件重复初始化

  // 生产环境优化配置
  compiler: {
    // 在生产构建时移除console语句
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error'] // 保留console.error，移除其他console语句
    } : false,
  },

  // 环境变量配置
  env: {
    NEXT_PUBLIC_LOG_LEVEL: process.env.NODE_ENV === 'production' ? 'error' : 'debug',
  },

  // Webpack优化配置
  webpack: (config, { isServer }) => {
    // 优化代码分割
    if (!isServer) {
      config.optimization.splitChunks = {
        ...config.optimization.splitChunks,
        cacheGroups: {
          ...config.optimization.splitChunks.cacheGroups,
          // Univer核心包
          univerCore: {
            test: /[\\/]node_modules[\\/]@univerjs[\\/](core|design|ui|docs|sheets|engine-formula|engine-render)[\\/]/,
            name: 'univer-core',
            chunks: 'all',
            priority: 30,
          },
          // Univer基础功能包
          univerBasic: {
            test: /[\\/]node_modules[\\/]@univerjs[\\/](sheets-filter|sheets-sort|sheets-conditional-formatting|sheets-table|sheets-data-validation)[\\/]/,
            name: 'univer-basic',
            chunks: 'async',
            priority: 20,
          },
          // Univer高级功能包
          univerPremium: {
            test: /[\\/]node_modules[\\/]@univerjs-pro[\\/]/,
            name: 'univer-premium',
            chunks: 'async',
            priority: 10,
          },
        },
      };
    }

    return config;
  },
};

export default nextConfig;
