import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: false, // 禁用严格模式以避免Univer组件重复初始化

  // 生产环境优化配置
  compiler: {
    // 在生产构建时移除console语句
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error'] // 保留console.error，移除其他console语句
    } : false,
  },

  // 环境变量配置
  env: {
    NEXT_PUBLIC_LOG_LEVEL: process.env.NODE_ENV === 'production' ? 'error' : 'debug',
  },
};

export default nextConfig;
