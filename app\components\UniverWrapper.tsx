'use client';

import dynamic from 'next/dynamic';
import { useState } from 'react';
import { UniverReadyCallback, UniverInstance, UniverAPI } from '@/types/univer';

const UniverSheet = dynamic(
  () => import('./UniverSheet'),
  { 
    ssr: false,
    loading: () => (
      <div className="flex flex-col items-center justify-center h-96 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        {/* <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div> */}
        {/* <div className="text-lg font-medium text-gray-700 mb-2">表格加载中，马上就好……</div> */}
        {/* <div className="text-sm text-gray-500">正在初始化Excel组件</div> */}
      </div>
    )
  }
);

interface UniverWrapperProps {
  onReady?: UniverReadyCallback;
  initialData?: Record<string, unknown>;
}

export default function UniverWrapper({ onReady, initialData }: UniverWrapperProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [loadingStage, setLoadingStage] = useState('初始化中...');

  const handleReady = (instance: UniverInstance, api: UniverAPI) => {
    // 延迟一点时间让用户看到加载完成的状态
    setLoadingStage('加载完成！');
    setTimeout(() => {
      setIsLoading(false);
      if (onReady) {
        onReady(instance, api);
      }
    }, 300);
  };

  // 模拟加载阶段更新
  useState(() => {
    const stages = [
      { text: '正在加载核心组件...', delay: 100 },
      { text: '正在初始化表格...', delay: 800 },
      { text: '正在准备工作区...', delay: 1500 },
    ];

    stages.forEach(({ text, delay }) => {
      setTimeout(() => {
        if (isLoading) {
          setLoadingStage(text);
        }
      }, delay);
    });
  });

  return (
    <div className="relative h-full">
      {isLoading && (
        <div className="absolute inset-0 z-10 flex flex-col items-center justify-start bg-white bg-opacity-95 rounded-lg backdrop-blur-sm">
          <div className="animate-spin rounded-full h-10 w-10 border-b-3 border-blue-600 mb-4 mt-16"></div>
          <div className="text-lg font-semibold text-gray-800 mb-2">表格加载中，马上就好……</div>
          <div className="text-sm text-gray-600 animate-pulse">{loadingStage}</div>
        </div>
      )}
      <UniverSheet onReady={handleReady} initialData={initialData} />
    </div>
  );
}