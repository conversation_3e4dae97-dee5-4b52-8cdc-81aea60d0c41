// Univer懒加载工具
import { LocaleType, merge } from "@univerjs/core";
import { log } from '@/app/lib/logger';

// 缓存已加载的插件，避免重复加载
const loadedPlugins = new Set<string>();

// 动态加载CSS的工具函数
function loadCSS(href: string): Promise<void> {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载过这个CSS
    const existingLink = document.querySelector(`link[href*="${href}"]`);
    if (existingLink) {
      resolve();
      return;
    }

    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.onload = () => resolve();
    link.onerror = () => reject(new Error(`Failed to load CSS: ${href}`));
    document.head.appendChild(link);
  });
}

// 基础功能插件懒加载器
export async function loadBasicPlugins(univerInstance: any) {
  const pluginKey = 'basic-plugins';
  if (loadedPlugins.has(pluginKey)) {
    log.debug('基础功能插件已加载，跳过重复加载');
    return;
  }

  try {
    log.debug('开始加载基础功能插件...');
    
    // 并行加载所有基础插件
    const [
      { UniverSheetsFilterPlugin },
      { UniverSheetsFilterUIPlugin },
      { UniverSheetsSortPlugin },
      { UniverSheetsSortUIPlugin },
      { UniverSheetsConditionalFormattingPlugin },
      { UniverSheetsConditionalFormattingUIPlugin },
      { UniverSheetsTablePlugin },
      { UniverSheetsTableUIPlugin },
      SheetsFilterUIZhCN,
      SheetsSortUIZhCN,
      SheetsConditionalFormattingUIZhCN,
      SheetsTableUIZhCN,
    ] = await Promise.all([
      import('@univerjs/sheets-filter'),
      import('@univerjs/sheets-filter-ui'),
      import('@univerjs/sheets-sort'),
      import('@univerjs/sheets-sort-ui'),
      import('@univerjs/sheets-conditional-formatting'),
      import('@univerjs/sheets-conditional-formatting-ui'),
      import('@univerjs/sheets-table'),
      import('@univerjs/sheets-table-ui'),
      import('@univerjs/sheets-filter-ui/locale/zh-CN'),
      import('@univerjs/sheets-sort-ui/locale/zh-CN'),
      import('@univerjs/sheets-conditional-formatting-ui/locale/zh-CN'),
      import('@univerjs/sheets-table-ui/locale/zh-CN'),
    ]);

    // 并行加载Facade API和CSS
    await Promise.all([
      import('@univerjs/sheets-conditional-formatting/facade'),
      import('@univerjs/sheets-filter/facade'),
      import('@univerjs/sheets-sort/facade'),
      import('@univerjs/sheets-table/facade'),
      // 动态加载CSS（仅在浏览器环境中）
      ...(typeof window !== 'undefined' ? [
        loadCSS('/_next/static/css/sheets-filter-ui.css'),
        loadCSS('/_next/static/css/sheets-sort-ui.css'),
        loadCSS('/_next/static/css/sheets-conditional-formatting-ui.css'),
        loadCSS('/_next/static/css/sheets-table-ui.css'),
      ] : []),
    ]);

    // 更新语言包
    const currentLocales = univerInstance.getLocales();
    const updatedLocales = merge(
      currentLocales[LocaleType.ZH_CN] || {},
      SheetsFilterUIZhCN.default,
      SheetsSortUIZhCN.default,
      SheetsConditionalFormattingUIZhCN.default,
      SheetsTableUIZhCN.default,
    );
    univerInstance.setLocale(LocaleType.ZH_CN, updatedLocales);

    // 注册插件
    univerInstance.registerPlugin(UniverSheetsFilterPlugin);
    univerInstance.registerPlugin(UniverSheetsFilterUIPlugin);
    univerInstance.registerPlugin(UniverSheetsSortPlugin);
    univerInstance.registerPlugin(UniverSheetsSortUIPlugin);
    univerInstance.registerPlugin(UniverSheetsConditionalFormattingPlugin);
    univerInstance.registerPlugin(UniverSheetsConditionalFormattingUIPlugin);
    univerInstance.registerPlugin(UniverSheetsTablePlugin);
    univerInstance.registerPlugin(UniverSheetsTableUIPlugin);

    loadedPlugins.add(pluginKey);
    log.debug('基础功能插件加载完成');
  } catch (error) {
    log.warn('基础功能插件加载失败:', error);
  }
}

// 高级功能插件懒加载器
export async function loadPremiumPlugins(univerInstance: any) {
  const pluginKey = 'premium-plugins';
  if (loadedPlugins.has(pluginKey)) {
    log.debug('高级功能插件已加载，跳过重复加载');
    return;
  }

  try {
    log.debug('开始加载高级功能插件...');
    
    // 并行加载所有高级插件
    const [
      { UniverSheetsChartPlugin },
      { UniverSheetsChartUIPlugin },
      { UniverSheetsPivotTablePlugin },
      { UniverSheetsPivotTableUIPlugin },
      SheetsChartZhCN,
      SheetsChartUIZhCN,
      SheetsPivotTableZhCN,
      SheetsPivotTableUIZhCN,
    ] = await Promise.all([
      import('@univerjs-pro/sheets-chart'),
      import('@univerjs-pro/sheets-chart-ui'),
      import('@univerjs-pro/sheets-pivot'),
      import('@univerjs-pro/sheets-pivot-ui'),
      import('@univerjs-pro/sheets-chart/locale/zh-CN'),
      import('@univerjs-pro/sheets-chart-ui/locale/zh-CN'),
      import('@univerjs-pro/sheets-pivot/locale/zh-CN'),
      import('@univerjs-pro/sheets-pivot-ui/locale/zh-CN'),
    ]);

    // 并行加载Facade API
    await Promise.all([
      import('@univerjs-pro/sheets-chart-ui/facade'),
      import('@univerjs-pro/sheets-pivot/facade'),
    ]);

    // 更新语言包
    const currentLocales = univerInstance.getLocales();
    const updatedLocales = merge(
      currentLocales[LocaleType.ZH_CN] || {},
      SheetsChartZhCN.default,
      SheetsChartUIZhCN.default,
      SheetsPivotTableZhCN.default,
      SheetsPivotTableUIZhCN.default,
    );
    univerInstance.setLocale(LocaleType.ZH_CN, updatedLocales);

    // 注册插件
    univerInstance.registerPlugin(UniverSheetsChartPlugin);
    univerInstance.registerPlugin(UniverSheetsChartUIPlugin);
    univerInstance.registerPlugin(UniverSheetsPivotTablePlugin);
    univerInstance.registerPlugin(UniverSheetsPivotTableUIPlugin);

    loadedPlugins.add(pluginKey);
    log.debug('高级功能插件加载完成');
  } catch (error) {
    log.warn('高级功能插件加载失败:', error);
  }
}

// 清理加载状态（用于组件卸载时）
export function clearLoadedPlugins() {
  loadedPlugins.clear();
}

// 检查插件是否已加载
export function isPluginLoaded(pluginKey: string): boolean {
  return loadedPlugins.has(pluginKey);
}

// 预加载策略：根据用户交互预测需要的插件
export function preloadPluginsOnDemand(action: string, univerInstance: any) {
  switch (action) {
    case 'filter':
    case 'sort':
    case 'format':
      // 用户开始使用基础功能，立即加载基础插件
      if (!isPluginLoaded('basic-plugins')) {
        loadBasicPlugins(univerInstance);
      }
      break;
    case 'chart':
    case 'pivot':
      // 用户开始使用高级功能，立即加载高级插件
      if (!isPluginLoaded('premium-plugins')) {
        loadPremiumPlugins(univerInstance);
      }
      break;
  }
}
