// app/UniverSheet.tsx
'use client';

import { useEffect, useRef } from 'react'

import { LocaleType, merge, Univer, UniverInstanceType } from "@univerjs/core";
import { FUniver } from "@univerjs/core/facade";
import { defaultTheme } from "@univerjs/design";

// 核心插件 - 立即加载
import { UniverFormulaEnginePlugin } from "@univerjs/engine-formula";
import { UniverRenderEnginePlugin } from "@univerjs/engine-render";
import { UniverUIPlugin } from "@univerjs/ui";
import { UniverDocsPlugin } from "@univerjs/docs";
import { UniverDocsUIPlugin } from "@univerjs/docs-ui";
import { UniverSheetsPlugin } from "@univerjs/sheets";
import { UniverSheetsUIPlugin } from "@univerjs/sheets-ui";
import { UniverSheetsFormulaPlugin } from "@univerjs/sheets-formula";
import { UniverSheetsFormulaUIPlugin } from "@univerjs/sheets-formula-ui";
import { UniverSheetsNumfmtPlugin } from "@univerjs/sheets-numfmt";
import { UniverSheetsNumfmtUIPlugin } from "@univerjs/sheets-numfmt-ui";

// 核心语言包 - 立即加载
import DesignZhCN from '@univerjs/design/locale/zh-CN';
import UIZhCN from '@univerjs/ui/locale/zh-CN';
import DocsUIZhCN from '@univerjs/docs-ui/locale/zh-CN';
import SheetsZhCN from '@univerjs/sheets/locale/zh-CN';
import SheetsUIZhCN from '@univerjs/sheets-ui/locale/zh-CN';
import SheetsFormulaUIZhCN from '@univerjs/sheets-formula-ui/locale/zh-CN';
import SheetsNumfmtUIZhCN from '@univerjs/sheets-numfmt-ui/locale/zh-CN';

// 数据验证插件 - 基础功能，立即加载
import { UniverDataValidationPlugin } from '@univerjs/data-validation';
import { UniverSheetsDataValidationPlugin } from '@univerjs/sheets-data-validation';
import { UniverSheetsDataValidationUIPlugin } from '@univerjs/sheets-data-validation-ui';
import SheetsDataValidationZhCN from '@univerjs/sheets-data-validation-ui/locale/zh-CN';

// 注意：其他插件将通过动态导入懒加载

// 核心 Facade API - 立即加载
import '@univerjs/engine-formula/facade';
import '@univerjs/ui/facade';
import '@univerjs/docs-ui/facade';
import '@univerjs/sheets/facade';
import '@univerjs/sheets-ui/facade';
import '@univerjs/sheets-formula/facade';
import '@univerjs/sheets-numfmt/facade';
import '@univerjs/sheets-data-validation/facade';

// 核心 CSS - 立即加载
import "@univerjs/design/lib/index.css";
import "@univerjs/ui/lib/index.css";
import "@univerjs/docs-ui/lib/index.css";
import "@univerjs/sheets-ui/lib/index.css";
import "@univerjs/sheets-formula-ui/lib/index.css";
import "@univerjs/sheets-numfmt-ui/lib/index.css";
import '@univerjs/sheets-data-validation-ui/lib/index.css';

// 懒加载插件的CSS - 立即加载样式但插件延迟加载
// 基础功能插件样式
import '@univerjs/sheets-filter-ui/lib/index.css';
import '@univerjs/sheets-sort-ui/lib/index.css';
import '@univerjs/sheets-conditional-formatting-ui/lib/index.css';
import '@univerjs/sheets-table-ui/lib/index.css';

// 高级功能插件样式
import '@univerjs-pro/sheets-chart-ui/lib/index.css';
import '@univerjs-pro/sheets-pivot-ui/lib/index.css';

import { UniverReadyCallback, UniverInstance, UniverAPI, CellData, WorkbookData } from '@/types/univer';
import { log } from '@/app/lib/logger';
import { loadBasicPlugins, loadPremiumPlugins, clearLoadedPlugins } from './univer-lazy-loader';

interface UniverSheetProps {
  onReady?: UniverReadyCallback;
  initialData?: Record<string, unknown>;
}

export default function UniverSheet({ onReady, initialData }: UniverSheetProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const univerAPIRef = useRef<UniverAPI | null>(null);
  const univerInstanceRef = useRef<UniverInstance | null>(null);
  const isMountedRef = useRef<boolean>(true);

  const initialDataRef = useRef(initialData);
  const onReadyRef = useRef(onReady);
  const isDataInitializedRef = useRef(false); // 添加标记防止重复初始化数据
  
  // 更新初始数据的函数
  const updateInitialData = (newData: Record<string, unknown>) => {
    if (!isMountedRef.current || !univerAPIRef.current || !newData || Object.keys(newData).length === 0) {
      return;
    }

    // 防止重复初始化相同的数据
    if (isDataInitializedRef.current && JSON.stringify(newData) === JSON.stringify(initialDataRef.current)) {
      log.debug('数据已初始化，跳过重复更新');
      return;
    }

    if (univerAPIRef.current && newData && Object.keys(newData).length > 0) {
      const activeWorkbook = univerAPIRef.current.getActiveWorkbook();
      if (activeWorkbook) {
        const activeWorksheet = activeWorkbook.getActiveSheet() as import('@/types/univer').UniverWorksheet | null;
        if (activeWorksheet) {
          Object.entries(newData).forEach(([cell, value]) => {
            // 解析单元格地址，如 "B1" -> {row: 0, col: 1}
            const match = cell.match(/^([A-Z]+)(\d+)$/);
            if (match) {
              const colStr = match[1];
              const rowNum = parseInt(match[2]) - 1; // 转换为0基索引
              // 将列字母转换为数字
              let colNum = 0;
              for (let i = 0; i < colStr.length; i++) {
                colNum = colNum * 26 + (colStr.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
              }
              colNum -= 1; // 转换为0基索引
              // 处理数据类型
              let cellValue = value;
              if (typeof value === 'number') {
                cellValue = value;
              } else if (typeof value === 'string' && !isNaN(Number(value)) && value.trim() !== '') {
                cellValue = Number(value);
              }
              // 优先用 getRangeByIndexes，如果没有则 fallback 到 getRange
              const worksheetAPI = activeWorksheet as unknown as {
                getRangeByIndexes?: (row: number, col: number) => { setValue: (value: unknown) => void };
                getRange?: (range: string) => { setValue: (value: unknown) => void };
              };
              if (typeof worksheetAPI.getRangeByIndexes === 'function') {
                worksheetAPI.getRangeByIndexes(rowNum, colNum).setValue(cellValue);
              } else if (typeof worksheetAPI.getRange === 'function') {
                // 将行列号转换为 A1 格式
                const cellAddress = String.fromCharCode(65 + colNum) + (rowNum + 1);
                worksheetAPI.getRange(cellAddress).setValue(cellValue);
              }
            }
          });
          log.debug('初始数据已更新到工作表:', newData);
          isDataInitializedRef.current = true; // 标记数据已初始化
        }
      }
    }
  };

  // 更新onReady回调引用
  useEffect(() => {
    onReadyRef.current = onReady;
  }, [onReady]);

  // 监听initialData变化，但只在真正需要时更新
  // useEffect(() => {
  //   // 只有当数据真正发生变化且组件已挂载时才更新
  //   if (isMountedRef.current && initialData &&
  //       JSON.stringify(initialData) !== JSON.stringify(initialDataRef.current)) {
  //     log.debug('检测到初始数据变化，准备更新:', initialData);
  //     initialDataRef.current = initialData;
  //     // 重置初始化标记，允许新数据的初始化
  //     isDataInitializedRef.current = false;
  //     updateInitialData(initialData);
  //   }
  // }, [initialData]);

  useEffect(() => {
    // 防止重复初始化
    if (univerInstanceRef.current) {
      return;
    }

    isMountedRef.current = true;
    let advancedPluginTimer: NodeJS.Timeout | null = null;

    const initUniver = async () => {
      try {
        const univer = new Univer({
          theme: defaultTheme,
          locale: LocaleType.ZH_CN,
          locales: {
            [LocaleType.ZH_CN]: merge(
              {},
              DesignZhCN,
              UIZhCN,
              DocsUIZhCN,
              SheetsZhCN,
              SheetsUIZhCN,
              SheetsFormulaUIZhCN,
              SheetsNumfmtUIZhCN,
              SheetsDataValidationZhCN,
            ),
          },
        });
         
        // 核心插件 - 优先加载
        univer.registerPlugin(UniverRenderEnginePlugin);
        univer.registerPlugin(UniverFormulaEnginePlugin);
         
        univer.registerPlugin(UniverUIPlugin, {
          container: containerRef.current!,
          // 隐藏“插入”菜单中的图片菜单项，因为有bug不显示title
          menu: {
            'sheet.menu.image': {
              hidden: true,
              disabled: true
            }
          },
        });
         
        univer.registerPlugin(UniverDocsPlugin);
        univer.registerPlugin(UniverDocsUIPlugin);
         
        univer.registerPlugin(UniverSheetsPlugin);
        univer.registerPlugin(UniverSheetsUIPlugin);
        univer.registerPlugin(UniverSheetsFormulaPlugin);
        univer.registerPlugin(UniverSheetsFormulaUIPlugin);
        univer.registerPlugin(UniverSheetsNumfmtPlugin);
        univer.registerPlugin(UniverSheetsNumfmtUIPlugin);

        // 注册数据验证插件 - 基础功能
        univer.registerPlugin(UniverDataValidationPlugin);
        univer.registerPlugin(UniverSheetsDataValidationPlugin);
        univer.registerPlugin(UniverSheetsDataValidationUIPlugin, {
            // 是否在下拉菜单中显示编辑按钮
              showEditOnDropdown: false
        });

        log.debug('核心插件加载完成');

        // 分阶段懒加载插件
        // 第一阶段：基础功能插件（300ms后）
        advancedPluginTimer = setTimeout(() => {
          if (isMountedRef.current && univerInstanceRef.current) {
            loadBasicPlugins(univerInstanceRef.current);
          }
        }, 300);

        // 第二阶段：高级功能插件（1.5秒后）
        setTimeout(() => {
          if (isMountedRef.current && univerInstanceRef.current) {
            loadPremiumPlugins(univerInstanceRef.current);
          }
        }, 1500);
        
        // 准备工作表数据，包含初始数据
        const workbookData: WorkbookData = {
          id: 'workbook-01',
          locale: LocaleType.ZH_CN,
          name: 'UniverSheet',
          sheetOrder: ['sheet-01'],
          sheets: {
            'sheet-01': {
              id: 'sheet-01',
              name: '工作表1',
              cellData: {},
            },
          },
        };

        // 如果有初始数据，直接添加到工作表数据中
        if (initialDataRef.current && Object.keys(initialDataRef.current).length > 0) {
          Object.entries(initialDataRef.current).forEach(([cell, value]) => {
            // 解析单元格地址，如 "B1" -> {row: 0, col: 1}
            const match = cell.match(/^([A-Z]+)(\d+)$/);
            if (match) {
              const colStr = match[1];
              const rowNum = parseInt(match[2]) - 1; // 转换为0基索引
              
              // 将列字母转换为数字
              let colNum = 0;
              for (let i = 0; i < colStr.length; i++) {
                colNum = colNum * 26 + (colStr.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
              }
              colNum -= 1; // 转换为0基索引
              
              if (!workbookData.sheets['sheet-01'].cellData[rowNum]) {
                workbookData.sheets['sheet-01'].cellData[rowNum] = {};
              }
              
              // 处理数据类型，确保数字被正确识别和格式化
              let cellValue = value;
              let cellType: number | undefined;
              
              // 如果是数字类型，确保正确设置
              if (typeof value === 'number') {
                cellValue = value;
                cellType = 2; // 数字类型
              }
              // 如果是字符串且看起来像数字，转换为数字
              else if (typeof value === 'string' && !isNaN(Number(value)) && value.trim() !== '') {
                cellValue = Number(value);
                cellType = 2; // 数字类型
              }
              
              const cellData: CellData = {
                v: cellValue,
              };
              
              // 如果是数字类型，设置类型标识
              if (cellType !== undefined) {
                cellData.t = cellType;
              }
              
              workbookData.sheets['sheet-01'].cellData[rowNum][colNum] = cellData;
            }
          });
          log.debug('初始数据已集成到工作表:', initialDataRef.current);
        }

        univer.createUnit(UniverInstanceType.UNIVER_SHEET, workbookData);
   
        const univerAPI = FUniver.newAPI(univer);
        univerAPIRef.current = univerAPI as UniverAPI;
        univerInstanceRef.current = univer;
        
        log.univer('Univer实例已准备就绪');
        
        // 将API暴露到全局变量
        (window as unknown as Record<string, unknown>).univerAPI = univerAPI;
        (window as unknown as Record<string, unknown>).univerInstance = univer;
        
        // 通知父组件Univer实例已准备就绪
        if (onReadyRef.current) {
          onReadyRef.current(univer, univerAPI as UniverAPI);
        }
      } catch (error) {
        log.error('Univer初始化失败:', error);
      }
    };

    initUniver();

    return () => {
      // 标记组件已卸载
      isMountedRef.current = false;

      // 清理定时器
      if (advancedPluginTimer) {
        clearTimeout(advancedPluginTimer);
      }

      // 清理懒加载状态
      clearLoadedPlugins();

      // 清理资源
      try {
        if (univerAPIRef.current) {
          univerAPIRef.current.dispose?.();
          univerAPIRef.current = null;
        }
        if (univerInstanceRef.current) {
          univerInstanceRef.current.dispose?.();
          univerInstanceRef.current = null;
        }
      } catch (error) {
        // 只在组件仍然挂载时记录错误
        if (isMountedRef.current) {
          log.error('Univer清理失败:', error);
        }
      }
    };
  }, []); // 移除所有依赖，防止重复初始化

  return (
    <div 
      ref={containerRef} 
      className="univer-container"
      // style={{ height: '80vh', width: '50%' }}
    />
  );
}