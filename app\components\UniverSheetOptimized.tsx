// app/UniverSheetOptimized.tsx
'use client';

import { useEffect, useRef } from 'react'

import { LocaleType, merge, Univer, UniverInstanceType } from "@univerjs/core";
import { FUniver } from "@univerjs/core/facade";
import { defaultTheme } from "@univerjs/design";
 
// 核心插件 - 立即加载
import { UniverFormulaEnginePlugin } from "@univerjs/engine-formula";
import { UniverRenderEnginePlugin } from "@univerjs/engine-render";
import { UniverUIPlugin } from "@univerjs/ui";
import { UniverDocsPlugin } from "@univerjs/docs";
import { UniverDocsUIPlugin } from "@univerjs/docs-ui";
import { UniverSheetsPlugin } from "@univerjs/sheets";
import { UniverSheetsUIPlugin } from "@univerjs/sheets-ui";
import { UniverSheetsFormulaPlugin } from "@univerjs/sheets-formula";
import { UniverSheetsFormulaUIPlugin } from "@univerjs/sheets-formula-ui";
import { UniverSheetsNumfmtPlugin } from "@univerjs/sheets-numfmt";
import { UniverSheetsNumfmtUIPlugin } from "@univerjs/sheets-numfmt-ui";

// 数据验证插件 - 基础功能，立即加载
import { UniverDataValidationPlugin } from '@univerjs/data-validation';
import { UniverSheetsDataValidationPlugin } from '@univerjs/sheets-data-validation';
import { UniverSheetsDataValidationUIPlugin } from '@univerjs/sheets-data-validation-ui';

// 核心语言包 - 立即加载
import DesignZhCN from '@univerjs/design/locale/zh-CN';
import UIZhCN from '@univerjs/ui/locale/zh-CN';
import DocsUIZhCN from '@univerjs/docs-ui/locale/zh-CN';
import SheetsZhCN from '@univerjs/sheets/locale/zh-CN';
import SheetsUIZhCN from '@univerjs/sheets-ui/locale/zh-CN';
import SheetsFormulaUIZhCN from '@univerjs/sheets-formula-ui/locale/zh-CN';
import SheetsNumfmtUIZhCN from '@univerjs/sheets-numfmt-ui/locale/zh-CN';
import SheetsDataValidationZhCN from '@univerjs/sheets-data-validation-ui/locale/zh-CN';

// 核心 Facade API - 立即加载
import '@univerjs/engine-formula/facade';
import '@univerjs/ui/facade';
import '@univerjs/docs-ui/facade';
import '@univerjs/sheets/facade';
import '@univerjs/sheets-ui/facade';
import '@univerjs/sheets-formula/facade';
import '@univerjs/sheets-numfmt/facade';
import '@univerjs/sheets-data-validation/facade';

// 核心 CSS - 立即加载
import "@univerjs/design/lib/index.css";
import "@univerjs/ui/lib/index.css";
import "@univerjs/docs-ui/lib/index.css";
import "@univerjs/sheets-ui/lib/index.css";
import "@univerjs/sheets-formula-ui/lib/index.css";
import "@univerjs/sheets-numfmt-ui/lib/index.css";
import '@univerjs/sheets-data-validation-ui/lib/index.css';

import { UniverReadyCallback, UniverInstance, UniverAPI, CellData, WorkbookData } from '@/types/univer';
import { log } from '@/app/lib/logger';

interface UniverSheetProps {
  onReady?: UniverReadyCallback;
  initialData?: Record<string, unknown>;
}

export default function UniverSheetOptimized({ onReady, initialData }: UniverSheetProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const univerAPIRef = useRef<UniverAPI | null>(null);
  const univerInstanceRef = useRef<UniverInstance | null>(null);
  const isMountedRef = useRef<boolean>(true);

  const initialDataRef = useRef(initialData);
  const onReadyRef = useRef(onReady);
  
  // 更新onReady回调引用
  useEffect(() => {
    onReadyRef.current = onReady;
  }, [onReady]);

  useEffect(() => {
    // 防止重复初始化
    if (univerInstanceRef.current) {
      return;
    }

    isMountedRef.current = true;
    let lazyLoadTimer: NodeJS.Timeout | null = null;

    const initUniver = async () => {
      try {
        const univer = new Univer({
          theme: defaultTheme,
          locale: LocaleType.ZH_CN,
          locales: {
            [LocaleType.ZH_CN]: merge(
              {},
              DesignZhCN,
              UIZhCN,
              DocsUIZhCN,
              SheetsZhCN,
              SheetsUIZhCN,
              SheetsFormulaUIZhCN,
              SheetsNumfmtUIZhCN,
              SheetsDataValidationZhCN,
            ),
          },
        });
         
        // 核心插件 - 立即加载
        univer.registerPlugin(UniverRenderEnginePlugin);
        univer.registerPlugin(UniverFormulaEnginePlugin);
         
        univer.registerPlugin(UniverUIPlugin, {
          container: containerRef.current!,
          menu: {
            'sheet.menu.image': {
              hidden: true,
              disabled: true
            }
          },
        });
         
        univer.registerPlugin(UniverDocsPlugin);
        univer.registerPlugin(UniverDocsUIPlugin);
         
        univer.registerPlugin(UniverSheetsPlugin);
        univer.registerPlugin(UniverSheetsUIPlugin);
        univer.registerPlugin(UniverSheetsFormulaPlugin);
        univer.registerPlugin(UniverSheetsFormulaUIPlugin);
        univer.registerPlugin(UniverSheetsNumfmtPlugin);
        univer.registerPlugin(UniverSheetsNumfmtUIPlugin);

        // 注册数据验证插件 - 基础功能
        univer.registerPlugin(UniverDataValidationPlugin);
        univer.registerPlugin(UniverSheetsDataValidationPlugin);
        univer.registerPlugin(UniverSheetsDataValidationUIPlugin, {
            showEditOnDropdown: false
        });

        log.debug('核心插件加载完成');

        // 懒加载其他插件
        const loadAdditionalPlugins = async () => {
          if (!isMountedRef.current || !univerInstanceRef.current) {
            return;
          }

          try {
            // 动态导入其他插件
            const modules = await Promise.all([
              import('@univerjs/sheets-filter'),
              import('@univerjs/sheets-filter-ui'),
              import('@univerjs/sheets-sort'),
              import('@univerjs/sheets-sort-ui'),
              import('@univerjs/sheets-conditional-formatting'),
              import('@univerjs/sheets-conditional-formatting-ui'),
              import('@univerjs/sheets-table'),
              import('@univerjs/sheets-table-ui'),
            ]);

            const [
              { UniverSheetsFilterPlugin },
              { UniverSheetsFilterUIPlugin },
              { UniverSheetsSortPlugin },
              { UniverSheetsSortUIPlugin },
              { UniverSheetsConditionalFormattingPlugin },
              { UniverSheetsConditionalFormattingUIPlugin },
              { UniverSheetsTablePlugin },
              { UniverSheetsTableUIPlugin },
            ] = modules;

            const currentUniver = univerInstanceRef.current;
            
            // 注册基础功能插件
            currentUniver.registerPlugin(UniverSheetsFilterPlugin);
            currentUniver.registerPlugin(UniverSheetsFilterUIPlugin);
            currentUniver.registerPlugin(UniverSheetsSortPlugin);
            currentUniver.registerPlugin(UniverSheetsSortUIPlugin);
            currentUniver.registerPlugin(UniverSheetsConditionalFormattingPlugin);
            currentUniver.registerPlugin(UniverSheetsConditionalFormattingUIPlugin);
            currentUniver.registerPlugin(UniverSheetsTablePlugin);
            currentUniver.registerPlugin(UniverSheetsTableUIPlugin);
            
            log.debug('基础功能插件延迟加载完成');

            // 继续加载高级插件
            setTimeout(async () => {
              if (!isMountedRef.current || !univerInstanceRef.current) {
                return;
              }

              try {
                const premiumModules = await Promise.all([
                  import('@univerjs-pro/sheets-chart'),
                  import('@univerjs-pro/sheets-chart-ui'),
                  import('@univerjs-pro/sheets-pivot'),
                  import('@univerjs-pro/sheets-pivot-ui'),
                ]);

                const [
                  { UniverSheetsChartPlugin },
                  { UniverSheetsChartUIPlugin },
                  { UniverSheetsPivotTablePlugin },
                  { UniverSheetsPivotTableUIPlugin },
                ] = premiumModules;

                const currentUniver = univerInstanceRef.current;
                currentUniver.registerPlugin(UniverSheetsChartPlugin);
                currentUniver.registerPlugin(UniverSheetsChartUIPlugin);
                currentUniver.registerPlugin(UniverSheetsPivotTablePlugin);
                currentUniver.registerPlugin(UniverSheetsPivotTableUIPlugin);
                
                log.debug('高级功能插件延迟加载完成');
              } catch (error) {
                log.warn('高级功能插件加载失败:', error);
              }
            }, 1000);

          } catch (error) {
            log.warn('基础功能插件加载失败:', error);
          }
        };

        // 延迟500ms加载其他插件
        lazyLoadTimer = setTimeout(loadAdditionalPlugins, 500);
        
        // 准备工作表数据
        const workbookData: WorkbookData = {
          id: 'workbook-01',
          locale: LocaleType.ZH_CN,
          name: 'UniverSheet',
          sheetOrder: ['sheet-01'],
          sheets: {
            'sheet-01': {
              id: 'sheet-01',
              name: '工作表1',
              cellData: {},
            },
          },
        };

        // 如果有初始数据，直接添加到工作表数据中
        if (initialDataRef.current && Object.keys(initialDataRef.current).length > 0) {
          Object.entries(initialDataRef.current).forEach(([cell, value]) => {
            const match = cell.match(/^([A-Z]+)(\d+)$/);
            if (match) {
              const colStr = match[1];
              const rowNum = parseInt(match[2]) - 1;
              
              let colNum = 0;
              for (let i = 0; i < colStr.length; i++) {
                colNum = colNum * 26 + (colStr.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
              }
              colNum -= 1;
              
              if (!workbookData.sheets['sheet-01'].cellData[rowNum]) {
                workbookData.sheets['sheet-01'].cellData[rowNum] = {};
              }
              
              let cellValue = value;
              let cellType: number | undefined;
              
              if (typeof value === 'number') {
                cellValue = value;
                cellType = 2;
              } else if (typeof value === 'string' && !isNaN(Number(value)) && value.trim() !== '') {
                cellValue = Number(value);
                cellType = 2;
              }
              
              const cellData: CellData = { v: cellValue };
              if (cellType !== undefined) {
                cellData.t = cellType;
              }
              
              workbookData.sheets['sheet-01'].cellData[rowNum][colNum] = cellData;
            }
          });
          log.debug('初始数据已集成到工作表:', initialDataRef.current);
        }

        univer.createUnit(UniverInstanceType.UNIVER_SHEET, workbookData);
   
        const univerAPI = FUniver.newAPI(univer);
        univerAPIRef.current = univerAPI as UniverAPI;
        univerInstanceRef.current = univer;
        
        log.univer('Univer实例已准备就绪');
        
        // 将API暴露到全局变量
        (window as unknown as Record<string, unknown>).univerAPI = univerAPI;
        (window as unknown as Record<string, unknown>).univerInstance = univer;
        
        // 通知父组件Univer实例已准备就绪
        if (onReadyRef.current) {
          onReadyRef.current(univer, univerAPI as UniverAPI);
        }
      } catch (error) {
        log.error('Univer初始化失败:', error);
      }
    };

    initUniver();

    return () => {
      isMountedRef.current = false;
      
      if (lazyLoadTimer) {
        clearTimeout(lazyLoadTimer);
      }
      
      try {
        if (univerAPIRef.current) {
          univerAPIRef.current.dispose?.();
          univerAPIRef.current = null;
        }
        if (univerInstanceRef.current) {
          univerInstanceRef.current.dispose?.();
          univerInstanceRef.current = null;
        }
      } catch (error) {
        if (isMountedRef.current) {
          log.error('Univer清理失败:', error);
        }
      }
    };
  }, []);

  return (
    <div 
      ref={containerRef} 
      className="univer-container"
    />
  );
}
