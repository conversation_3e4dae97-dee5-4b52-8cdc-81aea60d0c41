@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-geist-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-geist-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas,
    "Liberation Mono", Menlo, monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
} */

html,
body,
#__next {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: auto;
}

.univer-container {
  height: 100%;
  width: 100%;
  min-height: 500px;
}

/* 复制成功提示动画 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* 模态框动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out;
}


/* 修复Univer排序菜单位置问题(强制居中）*/
div[role="dialog"][data-state="open"] {
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}

/* 修复透视表字段面板空白区域过大的问题 */
/* 优化透视表字段面板高度和布局 */
div[role="complementary"] {
  max-height: 85vh !important;
  overflow-y: auto !important;
}

/* 透视表字段面板的具体修复 */
complementary {
  max-height: 85vh !important;
  overflow-y: auto !important;
}

/* 针对Univer组件的强制样式 */
.univer-container [role="complementary"] {
  max-height: 85vh !important;
  overflow-y: auto !important;
}

/* 通用的侧边栏高度限制 */
aside,
complementary,
[role="complementary"] {
  max-height: 85vh !important;
  overflow-y: auto !important;
}

/* 优化透视表拖拽区域的高度 */
/* 通用拖拽区域优化 */
[role="complementary"] div[style*="min-height"] {
  min-height: 35px !important;
  max-height: 55px !important;
}

/* 通过类名优化拖拽区域 */
[role="complementary"] *[class*="filter"],
[role="complementary"] *[class*="Filter"],
[role="complementary"] *[class*="column"],
[role="complementary"] *[class*="Column"],
[role="complementary"] *[class*="row"],
[role="complementary"] *[class*="Row"],
[role="complementary"] *[class*="value"],
[role="complementary"] *[class*="Value"] {
  min-height: 35px !important;
  max-height: 55px !important;
  padding: 6px !important;
}

/* 字段列表区域保持合适高度 */
[role="complementary"] div {
  max-height: 200px;
  overflow-y: auto;
}

/* 直接通过JavaScript注入的强力修复 */
.pivot-panel-optimized {
  max-height: 90vh !important;
  overflow-y: auto !important;
}

.pivot-drop-zone-optimized {
  min-height: 40px !important;
  max-height: 60px !important;
  height: 50px !important;
  padding: 8px !important;
}

/* 强力修复透视表面板 */
[role="complementary"] {
  max-height: 90vh !important;
  overflow-y: auto !important;
}

/* 强力修复拖拽区域高度 */
[role="complementary"] div[style*="height"] {
  max-height: 60px !important;
}

/* 通过更具体的选择器修复 */
[role="complementary"] > div > div > div {
  max-height: 60px !important;
}





