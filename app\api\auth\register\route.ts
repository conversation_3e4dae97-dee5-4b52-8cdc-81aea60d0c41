import { NextRequest, NextResponse } from 'next/server'
import { log } from '@/app/lib/logger';
import bcrypt from 'bcryptjs'
import { prisma } from '@/app/lib/db'
import { sendVerificationEmail, generateVerificationToken, generateTokenExpiry } from '@/app/lib/email'
import { validateInviteCode, useInviteCode, allocateFriendCodes } from '@/app/lib/invite-codes'

export async function POST(request: NextRequest) {
  try {
    const { email, username, password, inviteCode, agreeToTerms } = await request.json()

    // 验证输入
    if (!email || !username || !password) {
      return NextResponse.json(
        { error: '所有字段都是必填的' },
        { status: 400 }
      )
    }

    // 验证用户协议同意状态
    if (!agreeToTerms) {
      return NextResponse.json(
        { error: '请先阅读并同意用户服务协议和隐私政策' },
        { status: 400 }
      )
    }

    // 验证邀请码（如果提供）
    let userType = 'normal'
    let inviteCodeValidation = null

    if (inviteCode && inviteCode.trim()) {
      inviteCodeValidation = await validateInviteCode(inviteCode.trim())

      if (!inviteCodeValidation) {
        return NextResponse.json(
          { error: '邀请码不存在' },
          { status: 400 }
        )
      }

      if (!inviteCodeValidation.isValid) {
        return NextResponse.json(
          { error: '邀请码已被使用' },
          { status: 400 }
        )
      }

      // 设置用户类型
      userType = inviteCodeValidation.type === 'beta' ? 'beta' : 'friend'
    }

    // 检查邮箱是否在注销记录中且在30天内
    const deletedAccount = await prisma.deletedAccount.findUnique({
      where: { email }
    })

    if (deletedAccount) {
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      if (deletedAccount.deletedAt > thirtyDaysAgo) {
        const remainingDays = Math.ceil((deletedAccount.deletedAt.getTime() + 30 * 24 * 60 * 60 * 1000 - Date.now()) / (24 * 60 * 60 * 1000))
        return NextResponse.json(
          { error: `该邮箱已注销，还需等待 ${remainingDays} 天才能重新注册` },
          { status: 400 }
        )
      } else {
        // 超过30天，删除注销记录
        await prisma.deletedAccount.delete({
          where: { email }
        })
      }
    }

    // 检查用户是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email },
          { username }
        ]
      }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: '用户名或邮箱已存在' },
        { status: 400 }
      )
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12)

    // 生成邮箱验证令牌
    const verificationToken = generateVerificationToken()
    const verificationExpires = generateTokenExpiry()

    // 创建用户（未验证状态）
    const user = await prisma.user.create({
      data: {
        email,
        username,
        password: hashedPassword,
        emailVerified: false,
        emailVerificationToken: verificationToken,
        emailVerificationExpires: verificationExpires,
        userType,
        inviteCode: inviteCode ? inviteCode.trim() : null
      }
    })

    // 发送验证邮件
    try {
      await sendVerificationEmail(email, verificationToken)
    } catch (error) {
      // 如果邮件发送失败，删除已创建的用户
      await prisma.user.delete({ where: { id: user.id } })
      log.error('邮件发送失败:', error)
      return NextResponse.json(
        { error: '邮件发送失败，请稍后重试' },
        { status: 500 }
      )
    }

    // 如果使用了邀请码，标记为已使用
    if (inviteCode && inviteCodeValidation) {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const codeUsed = await useInviteCode(inviteCode.trim(), user.id)
      if (!codeUsed) {
        log.error('邀请码使用失败:', inviteCode)
        // 这里不删除用户，因为邮件已发送，只记录错误
      }
    }

    // 如果是内测用户，自动分配5个好友邀请码
    if (userType === 'beta') {
      try {
        await allocateFriendCodes(user.id, 5)
        log.debug(`为内测用户 ${user.username} 分配了5个好友邀请码`)
      } catch (error) {
        log.error('分配好友邀请码失败:', error)
        // 不影响注册流程，只记录错误
      }
    }

    return NextResponse.json(
      {
        message: '注册成功！请检查您的邮箱并点击验证链接完成注册。',
        user: {
          id: user.id,
          email: user.email,
          username: user.username
        }
      },
      { status: 201 }
    )
  } catch (error) {
    log.error('注册错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}