'use client'

import React, { useEffect } from 'react'
import Link from 'next/link'
import { useSession, signOut } from 'next-auth/react'
import { usePathname } from 'next/navigation'
import { useNavbar } from './NavbarContext'

export default function Navbar() {
  const { data: session } = useSession()
  const pathname = usePathname()
  const { showLevelList, levelListHref } = useNavbar()

  // 调试信息 - 在开发环境中显示，使用useEffect避免无限循环
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Navbar render:', { showLevelList, levelListHref, pathname })
    }
  }, [showLevelList, levelListHref, pathname])

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' })
  }

  // 不在登录/注册页面和主页显示导航栏
  if (pathname?.startsWith('/auth/') || pathname === '/') {
    return null
  }

  return (
    <nav className="bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8">
        <div className="flex justify-between h-16">
          {/* 左侧品牌和导航菜单 */}
          <div className="flex items-center space-x-2 sm:space-x-4 lg:space-x-8 min-w-0 flex-1">
            {/* 品牌Logo */}
            <Link
              href="/"
              className="flex items-center space-x-1 sm:space-x-2 group flex-shrink-0"
            >
              <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
                <span className="text-white font-bold text-xs sm:text-sm">🏠</span>
              </div>
              <span className="text-sm sm:text-base font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent hidden sm:block">
                首页
              </span>
            </Link>

            {/* 导航菜单 */}
            {session && (
              <div className="flex items-center space-x-1 min-w-0">
                <Link
                  href="/dashboard"
                  className={`px-2 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex-shrink-0 ${
                    pathname === '/dashboard'
                      ? 'text-green-600 bg-green-50 shadow-sm'
                      : 'text-gray-600 hover:text-green-600 hover:bg-green-50/50'
                  }`}
                >
                  <span className="flex items-center space-x-1">
                    <span>🗺️</span>
                    <span className="hidden sm:inline">主关卡</span>
                  </span>
                </Link>

                {showLevelList && (
                  <Link
                    href={levelListHref || "/dashboard"}
                    className="px-2 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium text-gray-600 hover:text-green-600 hover:bg-green-50/50 transition-all duration-200 flex-shrink-0"
                  >
                    <span className="flex items-center space-x-1">
                      <span>🧩</span>
                      <span className="hidden sm:inline">子关卡</span>
                    </span>
                  </Link>
                )}


              </div>
            )}
          </div>

          {/* 右侧用户信息 */}
          {session && (
            <div className="flex items-center space-x-1 sm:space-x-2 lg:space-x-4 flex-shrink-0">
              {/* 邀请码链接 */}
              <Link
                href="/invite-codes"
                className={`px-2 sm:px-3 py-1.5 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex-shrink-0 ${
                  pathname === '/invite-codes'
                    ? 'text-purple-600 bg-purple-50 shadow-sm'
                    : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50/50'
                }`}
              >
                <span className="flex items-center space-x-1">
                  <span>🎫</span>
                  <span className="hidden sm:inline">邀请码</span>
                </span>
              </Link>

              {/* 经验值显示 */}
              <div className="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1.5 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-full border border-blue-100">
                <span className="text-blue-600 text-sm">⭐</span>
                <span className="text-xs sm:text-sm font-medium text-blue-700">{session.user.score}</span>
                <span className="text-xs text-blue-500 hidden sm:inline">EXP</span>
              </div>

              {/* 用户头像和名称 */}
              <div className="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1.5 bg-gray-50 rounded-full border border-gray-200">
                {session.user.userType === 'beta' ? (
                  <Link href="/profile" className="relative group">
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src="/icon.svg"
                      alt="VIP"
                      className="w-5 h-5 sm:w-6 sm:h-6 cursor-pointer hover:scale-105 transition-transform duration-200"
                    />
                    {/* VIP悬停提示 */}
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-gradient-to-br from-yellow-300 via-amber-300 to-yellow-400 text-amber-800 text-sm font-semibold rounded-lg shadow-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none z-50 border border-amber-200 ring-1 ring-yellow-400/50">
                      <span className="text-amber-900 drop-shadow-sm">✨</span> 荣耀徽标
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-3 border-r-3 border-b-3 border-transparent border-b-yellow-300"></div>
                    </div>
                  </Link>
                ) : (
                  <Link href="/profile" className="relative group">
                    <div className="w-5 h-5 sm:w-6 sm:h-6 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full flex items-center justify-center cursor-pointer hover:scale-105 transition-transform duration-200">
                      <span className="text-white text-xs font-bold">
                        {session.user.username?.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  </Link>
                )}
                <div className="relative group">
                  <Link
                    href="/profile"
                    className="text-xs sm:text-sm font-medium text-gray-700 max-w-12 sm:max-w-20 truncate hidden sm:inline hover:text-green-600 transition-colors px-1 py-1"
                  >
                    {session.user.username}
                  </Link>
                  {/* 个人中心悬停提示 */}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none z-[9999]">
                    进入个人中心
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-800"></div>
                  </div>
                </div>
              </div>

              {/* 退出按钮 */}
              <button
                onClick={handleSignOut}
                className="px-2 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 border border-red-200 hover:border-red-300 transition-all duration-200"
              >
                <span className="hidden sm:inline">退出</span>
                <span className="sm:hidden">🚪</span>
              </button>
            </div>
          )}

          {!session && (
            <div className="flex items-center">
              <Link
                href="/auth/signin"
                className="px-6 py-2 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
              >
                登录
              </Link>
            </div>
          )}
        </div>
      </div>
    </nav>
  )
}
